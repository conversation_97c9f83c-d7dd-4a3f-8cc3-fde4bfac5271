<?php
/**
 * Direct email test - bypasses background processing
 * Use this to test if the email system works at all
 */

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/classes/phpmailercorreo.php';
require_once __ROOT__ . '/vendor/autoload.php';

echo "=== Direct Email Test ===\n\n";

// Test email parameters
$testParams = [
    'nombre_aliado'   => 'Test Aliado Directo',
    'correo_aliado'   => '<EMAIL>',      // Change this to a real email for testing
    'tipo_alianza'    => 'Socio estrategico',
    'telefono_aliado' => '+57 ************'
];

echo "Testing direct email sending...\n";
echo "Email parameters:\n";
print_r($testParams);

// Test 1: Welcome email
echo "\n1. Testing welcome email...\n";
try {
    PHPMailerCorreo::enviar_correo_aliado_comercial($testParams);
    echo "✓ Welcome email sent successfully\n";
} catch (Exception $e) {
    echo "✗ Welcome email failed: " . $e->getMessage() . "\n";
}

// Test 2: Management notification email
echo "\n2. Testing management notification email...\n";
try {
    global $conexion;
    if (!$conexion instanceof PDO) {
        echo "✗ Database connection not available\n";
    } else {
        PHPMailerCorreo::enviar_correo_gerencia_aliados_comerciales($testParams, $conexion);
        echo "✓ Management notification email sent successfully\n";
    }
} catch (Exception $e) {
    echo "✗ Management notification email failed: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "\nIf emails failed, check:\n";
echo "1. SMTP settings in PHPMailerCorreo class\n";
echo "2. Internet connectivity\n";
echo "3. Email provider authentication\n";
echo "4. Firewall settings\n";
echo "5. PHP mail extensions\n";
?>
