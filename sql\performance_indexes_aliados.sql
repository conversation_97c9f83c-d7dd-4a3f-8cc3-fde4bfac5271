-- Performance optimization indexes for Aliados Comerciales system
-- Run these to improve query performance

-- Indexes for aliados_comerciales table
CREATE INDEX IF NOT EXISTS idx_aliados_estado ON aliados_comerciales(estado);
CREATE INDEX IF NOT EXISTS idx_aliados_cedula_nit ON aliados_comerciales(cedula_nit);
CREATE INDEX IF NOT EXISTS idx_aliados_correo ON aliados_comerciales(correo_electronico);
CREATE INDEX IF NOT EXISTS idx_aliados_fecha_registro ON aliados_comerciales(fecha_registro);
CREATE INDEX IF NOT EXISTS idx_aliados_viable ON aliados_comerciales(viable);

-- Indexes for clientes_potenciales table
CREATE INDEX IF NOT EXISTS idx_clientes_pot_aliado_id ON clientes_potenciales(aliado_comercial_id);
CREATE INDEX IF NOT EXISTS idx_clientes_pot_estado ON clientes_potenciales(estado);
CREATE INDEX IF NOT EXISTS idx_clientes_pot_fecha_registro ON clientes_potenciales(fecha_registro);
CREATE INDEX IF NOT EXISTS idx_clientes_pot_correo ON clientes_potenciales(correo_contacto);

-- Indexes for servicios table
CREATE INDEX IF NOT EXISTS idx_servicios_categoria ON servicios(id_servicio_categoria);
CREATE INDEX IF NOT EXISTS idx_servicios_estado ON servicios(estado);
CREATE INDEX IF NOT EXISTS idx_servicios_prioridad ON servicios(prioridad);

-- Indexes for servicios_categorias table
CREATE INDEX IF NOT EXISTS idx_servicios_cat_estado ON servicios_categorias(estado);
CREATE INDEX IF NOT EXISTS idx_servicios_cat_prioridad ON servicios_categorias(prioridad);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_servicios_categoria_estado ON servicios(id_servicio_categoria, estado);
CREATE INDEX IF NOT EXISTS idx_servicios_cat_estado_prioridad ON servicios_categorias(estado, prioridad);

-- Index for the join query in AliadoComercial::get_list()
CREATE INDEX IF NOT EXISTS idx_clientes_pot_aliado_estado ON clientes_potenciales(aliado_comercial_id, estado);

-- Ensure clientes_potenciales_servicios indexes exist (they should from the original SQL)
-- These are critical for the service association queries
CREATE INDEX IF NOT EXISTS idx_cps_cliente_potencial ON clientes_potenciales_servicios(id_cliente_potencial);
CREATE INDEX IF NOT EXISTS idx_cps_servicio ON clientes_potenciales_servicios(id_servicio);
CREATE INDEX IF NOT EXISTS idx_cps_fecha_registro ON clientes_potenciales_servicios(fecha_registro);
